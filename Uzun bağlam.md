Uzun bağlam

Birçok Gemini modeli, 1 milyon veya daha fazla jetonluk büyük bağlam pencereleriyle birlikte gelir. Geçmişte büyük dil modelleri (LLM'ler), modele tek seferde iletilebilecek metin (veya jeton) miktarıyla önemli ölçüde sınırlıydı. Gemini'ın uzun bağlam penceresi, birçok yeni kullanım alanının ve geliştirici paradigmasının kilidini açar.

Metin oluşturma veya çok formatlı girişler gibi durumlar için kullandığınız kod, uzun bağlamla birlikte herhangi bir değişiklik yapılmadan çalışır.

Bu belgede, 1 milyon ve daha fazla parçalık bağlam pencerelerine sahip modelleri kullanarak neler yapabileceğinize dair genel bir bakış sunulmaktadır. <PERSON><PERSON> say<PERSON>da, bağlam penceresiyle ilgili kısa bir genel bakış sunulmakta ve geliştiricilerin uzun bağlam, uzun bağlamın çeşitli gerçek dünya kullanım alanları ve uzun bağlam kullanımını optimize etme yöntemleri hakkında nasıl düşünmesi gerektiği ele alınmaktadır.

Belirli modellerin bağlam penceresi boyutları için Modeller sayfasına bakın.

Bağlam penceresi nedir?
Gemini modellerini kullanmanın temel yolu, modele bilgi (bağlam) iletmektir. Model daha sonra bir yanıt oluşturur. Bağlam penceresi, kısa süreli belleğe benzetilebilir. Kısa süreli bellekte sınırlı miktarda bilgi saklanabilir. Bu durum, üretken modeller için de geçerlidir.

Modellerin nasıl çalıştığı hakkında daha fazla bilgiyi üretken modeller kılavuzumuzda bulabilirsiniz.

Uzun bağlamı kullanmaya başlama
Üretken modellerin önceki sürümleri,tek seferde yalnızca 8.000 jetonu işleyebiliyordu. Daha yeni modeller, 32.000 hatta 128.000 jeton kabul ederek bu sınırı daha da ileriye taşıdı. Gemini, 1 milyon jeton kabul edebilen ilk modeldir.

Pratikte 1 milyon jeton şöyle görünür:

50.000 satır kod (satır başına standart 80 karakterle)
Son 5 yılda gönderdiğiniz tüm kısa mesajlar
Ortalama uzunlukta 8 İngilizce roman
Ortalama uzunlukta 200'den fazla podcast bölümünün transkripti
Diğer birçok modelde yaygın olan daha sınırlı bağlam pencereleri genellikle jeton tasarrufu için eski mesajları rastgele bırakma, içeriği özetleme, vektör veritabanlarıyla RAG kullanma veya istemleri filtreleme gibi stratejiler gerektirir.

Bu teknikler belirli senaryolarda değerli olmaya devam etse de Gemini'ın geniş bağlam penceresi daha doğrudan bir yaklaşımı mümkün kılar: ilgili tüm bilgileri en baştan sağlamak. Gemini modelleri, büyük bağlam yetenekleriyle özel olarak geliştirildiğinden bağlam içi öğrenme konusunda güçlü bir performans gösterir. Örneğin, yalnızca bağlam içi eğitici materyaller (500 sayfalık bir referans dil bilgisi, bir sözlük ve yaklaşık 400 paralel cümle) kullanılarak Gemini, aynı materyalleri kullanan bir insan öğrenenle benzer kalitede, 200'den az konuşanı olan bir Papua dili olan Kalamang diline çeviri yapmayı öğrendi. Bu, Gemini'ın uzun bağlamı sayesinde mümkün olan paradigma değişikliğini gösterir. Bu değişiklik, bağlam içi güçlü öğrenme yoluyla yeni olanaklar sunar.

Uzun bağlam kullanım alanları
Çoğu üretken modelin standart kullanım alanı hâlâ metin girişi olsa da Gemini model ailesi, çok formatlı kullanım alanlarında yeni bir paradigma sunuyor. Bu modeller metin, video, ses ve görüntüleri doğal olarak anlayabilir. Bu API'ler, kolaylık sağlamak için çok formatlı dosya türlerini alan Gemini API ile birlikte sunulur.

Uzun metin
Metin, LLM'lerle ilgili gelişmelerin çoğunun temelinde yatan zeka katmanı olduğunu kanıtlamıştır. Daha önce de belirtildiği gibi, LLM'lerin pratik sınırlamalarının çoğu, belirli görevleri yerine getirmek için yeterince büyük bir bağlam penceresine sahip olmamalarından kaynaklanıyordu. Bu durum, almayla artırılmış üretim (RAG) ve modele dinamik olarak alakalı bağlamsal bilgiler sağlayan diğer tekniklerin hızla benimsenmesine yol açtı. Ancak bağlam pencereleri büyüdükçe yeni kullanım alanları sunan yeni teknikler de ortaya çıkıyor.

Metin tabanlı uzun bağlam için bazı yeni ve standart kullanım alanları şunlardır:

Büyük metin derlemelerini özetleme
Daha küçük bağlam modelleriyle önceki özetleme seçenekleri, yeni jetonlar modele aktarılırken önceki bölümlerin durumunu korumak için kayan bir pencere veya başka bir teknik gerektiriyordu.
Soru sorma ve yanıtlama
Geçmişte bu, yalnızca RAG ile mümkündü. Bunun nedeni, bağlam miktarının sınırlı olması ve modellerin olgusal hatırlama oranının düşük olmasıydı.
Temsilci tabanlı iş akışları
Metin, aracıların ne yaptıkları ve ne yapmaları gerektiğiyle ilgili durumu korumalarının temelini oluşturur. Dünya ve aracının hedefi hakkında yeterli bilgiye sahip olmamak, aracıların güvenilirliğini sınırlar.
Çok görevli bağlam içi öğrenme, uzun bağlam modellerinin sunduğu en benzersiz özelliklerden biridir. Araştırmalar, modele bir görevle ilgili bir veya birkaç örneğin sunulduğu ve bunun yüzlerce, binlerce hatta yüz binlerce örneğe çıkarıldığı yaygın "tek görevli" veya "çok görevli" örnek paradigmanın, modelin yeni yetenekler kazanmasını sağlayabileceğini göstermiştir. Bu çok görevli yaklaşımın, belirli bir görev için ince ayar yapılmış modellerle benzer performans gösterdiği de kanıtlanmıştır. Gemini modelinin performansının henüz üretime yönelik kullanıma sunulmaya uygun olmadığı kullanım alanlarında çok görevli yaklaşımı deneyebilirsiniz. Uzun bağlam optimizasyonu bölümünde daha sonra inceleyebileceğiniz gibi, bağlam önbelleğe alma bu tür yüksek giriş jetonu iş yükünü çok daha ekonomik ve bazı durumlarda daha düşük gecikmeli hale getirir.

Uzun video
Video içeriğinin faydası, uzun süredir ortamın erişilebilir olmaması nedeniyle kısıtlanıyordu. İçeriği gözden geçirmek zordu, transkriptler genellikle videonun nüansını yakalayamıyordu ve çoğu araç resim, metin ve sesi birlikte işlemiyordu. Gemini ile uzun bağlamlı metin özellikleri, çok formatlı girişlerle ilgili soruları akıl yürüterek yanıtlayabilme ve bu işlemleri tutarlı bir performansla yapabilme anlamına gelir.

Uzun video bağlamının bazı yeni ve standart kullanım alanları şunlardır:

Video soruları ve yanıtları
Google'ın Project Astra ile gösterildiği gibi video belleği
Video altyazıları
Mevcut meta verileri yeni çok formatlı anlayışla zenginleştirerek video öneri sistemleri
Veri kümesi ve ilişkili video meta verilerine bakıp izleyiciyle alakalı olmayan video bölümlerini kaldırarak videoları özelleştirme
Video içeriği denetimi
Gerçek zamanlı video işleme
Videolarla çalışırken videoların jetonlara nasıl işlendiğini göz önünde bulundurmak önemlidir. Bu durum, faturalandırma ve kullanım sınırlarını etkiler. Video dosyalarıyla istem oluşturma hakkında daha fazla bilgiyi İstem oluşturma kılavuzunda bulabilirsiniz.

Uzun ses içerikleri
Gemini modelleri, sesleri anlayabilen ilk doğal olarak çok formatlı büyük dil modelleriydi. Geçmişte, ses işlemek için genellikle birden fazla alana özgü model (ör. konuşmayı metne dönüştürme modeli ve metni metne dönüştürme modeli) birlikte kullanılırdı. Bu durum, birden fazla gidiş-dönüş isteği gerçekleştirilmesinden kaynaklanan ek gecikmeye ve genellikle birden fazla model kurulumunun bağlantısı kesilmiş mimarilerine atfedilen performans düşüşüne yol açtı.

Ses bağlamı için bazı yeni ve standart kullanım alanları şunlardır:

Anlık metne dönüştürme ve çeviri
Podcast / video soru ve cevap
Toplantıyı metne dönüştürme ve özetleme
Sesli asistanlar
Ses dosyalarıyla istem oluşturma hakkında daha fazla bilgiyi İstem oluşturma kılavuzu'nda bulabilirsiniz.

Uzun bağlam optimizasyonları
Uzun bağlam ve Gemini modelleriyle çalışırken birincil optimizasyon, bağlam önbelleğe almayı kullanmaktır. Tek bir istekte çok sayıda jetonun işlenmesinin daha önce mümkün olmamasının yanı sıra, diğer temel kısıtlama maliyetti. Kullanıcının 10 PDF, bir video ve bazı iş belgeleri yüklediği "verilerinizle sohbet edin" uygulamanız varsa bu istekleri işlemek için geçmişte daha karmaşık bir alma artırılmış oluşturma (RAG) aracı/çerçevesiyle çalışmanız ve bağlam penceresine taşınan jetonlar için önemli bir miktar ödemeniz gerekirdi. Artık kullanıcının yüklediği dosyaları önbelleğe alabilir ve saatlik olarak depolama için ödeme yapabilirsiniz. Örneğin, Gemini Flash ile istek başına giriş / çıkış maliyeti, standart giriş / çıkış maliyetinden yaklaşık 4 kat daha azdır. Bu nedenle, kullanıcı verileriyle yeterince sohbet ederse geliştirici olarak sizin için büyük bir maliyet tasarrufu sağlanır.

Uzun bağlam sınırlamaları
Bu kılavuzun çeşitli bölümlerinde, Gemini modellerinin çeşitli samanlıkta iğne arama değerlendirmelerinde nasıl yüksek performans elde ettiğinden bahsettik. Bu testlerde, aradığınız tek bir iğnenin bulunduğu en temel kurulum dikkate alınır. Birden fazla "iğne" veya aradığınız belirli bilgiler olması durumunda model aynı doğrulukla çalışmaz. Performans, bağlama bağlı olarak büyük ölçüde değişebilir. Doğru bilgilerin alınması ile maliyet arasında doğal bir denge olduğundan bu durumu göz önünde bulundurmak önemlidir. Tek bir sorguda yaklaşık% 99 doğruluk elde edebilirsiniz ancak bu sorguyu her gönderdiğinizde giriş jetonu maliyetini ödemeniz gerekir. Bu nedenle, 100 bilgi parçasının alınması için% 99 performans gerekiyorsa muhtemelen 100 istek göndermeniz gerekir. Bu, bağlam önbelleğe almanın, performansı yüksek tutarken Gemini modellerini kullanmayla ilişkili maliyeti önemli ölçüde azaltabileceği durumlara iyi bir örnektir.

SSS
Sorgumu bağlam penceresinde nereye yerleştirmeliyim?
Çoğu durumda, özellikle de toplam bağlam uzunsa sorgunuzu / sorunuzu istemin sonuna (diğer tüm bağlamlardan sonra) yerleştirdiğinizde modelin performansı daha iyi olur.

Bir sorguya daha fazla jeton eklediğimde model performansı düşer mi?
Genel olarak, jetonların modele iletilmesi gerekmiyorsa iletilmemesi en iyisidir. Ancak, bazı bilgileri içeren büyük bir jeton yığınınız varsa ve bu bilgiler hakkında soru sormak istiyorsanız model, bu bilgileri ayıklama konusunda oldukça başarılıdır (birçok durumda% 99'a varan doğruluk).

Uzun bağlamlı sorgularla maliyetimi nasıl düşürebilirim?
Çok kez yeniden kullanmak istediğiniz benzer bir jeton / bağlam kümeniz varsa bağlam önbelleğe alma, bu bilgilerle ilgili soru sormayla ilişkili maliyetleri azaltmanıza yardımcı olabilir.

Bağlam uzunluğu, model gecikmesini etkiler mi?
Boyutundan bağımsız olarak, her istekte belirli bir gecikme süresi vardır ancak genellikle daha uzun sorgularda gecikme süresi (ilk jetona kadar geçen süre) daha uzundur.