Jetonları anlama ve sayma

Python JavaScript Go



Gemini ve diğer üretken yapay zeka modelleri, giriş ve çıkışı jeton adı verilen bir ayrıntı düzeyinde işler.

Jetonlar hakkında
Jetonlar, z gibi tek karakterler veya cat gibi tam kelimeler olabilir. Uzun kelimeler birkaç jetona ayrılır. Model tarafından kullanılan tüm jetonlar kümesine kelime hazinesi, metni jetonlara bölme işlemine ise jetonlaştırma adı verilir.

Gemini modellerinde bir jeton yaklaşık 4 karaktere eşittir. 100 jeton yaklaşık 60-80 İngilizce kelimeye eşittir.

Faturalandırma etkinleştirildiğinde Gemini API'ye yapılan bir çağrının maliyeti kısmen giriş ve çıkış jetonlarının sayısına göre belirlenir. <PERSON><PERSON> nedenle, jetonları nasıl sayacağınızı bilmek faydalı olabilir.

<PERSON><PERSON><PERSON> say<PERSON>, resim dosyaları ve metin dışı diğer formatlar da dahil olmak üzere Gemini API'ye yapılan tüm girişler ve API'den alınan tüm çıkışlar jetonlaştırılır.

Jetonları aşağıdaki şekillerde sayabilirsiniz:

İsteği girerek countTokens numarasını arayın.
Bu, yalnızca girişteki toplam jeton sayısını döndürür. İsteklerinizin boyutunu kontrol etmek için girişi modele göndermeden önce bu aramayı yapabilirsiniz.

generate_content numarası arandıktan sonra response nesnesinde usageMetadata özelliğini kullanın.
Bu, hem girişte hem de çıkışta toplam jeton sayısını döndürür: totalTokenCount.
Ayrıca, giriş ve çıkışın jeton sayılarını ayrı ayrı döndürür: promptTokenCount (giriş jetonları) ve candidatesTokenCount (çıkış jetonları).

Metin jetonlarını sayma
countTokens işlevini yalnızca metin içeren bir girişle çağırırsanız yalnızca girişteki metnin jeton sayısını (totalTokens) döndürür. İsteklerinizin boyutunu kontrol etmek için generateContent işlevini çağırmadan önce bu çağrıyı yapabilirsiniz.

Diğer bir seçenek de generateContent numaralı telefonu arayıp usageMetadata özelliğini response nesnesinde kullanarak aşağıdakileri elde etmektir:

Girişin (promptTokenCount) ve çıkışın (candidatesTokenCount) ayrı jeton sayıları
Hem girişte hem de çıkışta toplam jeton sayısı (totalTokenCount)

// Make sure to include the following import:
// import {GoogleGenAI} from '@google/genai';
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const prompt = "The quick brown fox jumps over the lazy dog.";
const countTokensResponse = await ai.models.countTokens({
  model: "gemini-2.0-flash",
  contents: prompt,
});
console.log(countTokensResponse.totalTokens);

const generateResponse = await ai.models.generateContent({
  model: "gemini-2.0-flash",
  contents: prompt,
});
console.log(generateResponse.usageMetadata);

Çok adımlı (sohbet) jetonlarını sayma
Sohbet geçmişiyle countTokens işlevini çağırırsanız sohbetteki her rolden gelen metnin toplam jeton sayısını (totalTokens) döndürür.

Diğer bir seçenek de sendMessage numaralı telefonu arayıp usageMetadata özelliğini response nesnesinde kullanarak aşağıdakileri elde etmektir:

Girişin (promptTokenCount) ve çıkışın (candidatesTokenCount) ayrı jeton sayıları
Hem girişte hem de çıkışta toplam jeton sayısı (totalTokenCount)
Bir sonraki etkileşiminizin ne kadar büyük olacağını anlamak için countTokens işlevini çağırırken bunu geçmişe eklemeniz gerekir.


// Make sure to include the following import:
// import {GoogleGenAI} from '@google/genai';
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
// Initial chat history.
const history = [
  { role: "user", parts: [{ text: "Hi my name is Bob" }] },
  { role: "model", parts: [{ text: "Hi Bob!" }] },
];
const chat = ai.chats.create({
  model: "gemini-2.0-flash",
  history: history,
});

// Count tokens for the current chat history.
const countTokensResponse = await ai.models.countTokens({
  model: "gemini-2.0-flash",
  contents: chat.getHistory(),
});
console.log(countTokensResponse.totalTokens);

const chatResponse = await chat.sendMessage({
  message: "In one sentence, explain how a computer works to a young child.",
});
console.log(chatResponse.usageMetadata);

// Add an extra user message to the history.
const extraMessage = {
  role: "user",
  parts: [{ text: "What is the meaning of life?" }],
};
const combinedHistory = chat.getHistory();
combinedHistory.push(extraMessage);
const combinedCountTokensResponse = await ai.models.countTokens({
  model: "gemini-2.0-flash",
  contents: combinedHistory,
});
console.log(
  "Combined history token count:",
  combinedCountTokensResponse.totalTokens,
);

Çok formatlı parçaları sayma
Gemini API'ye yapılan tüm girişler (metin, resim dosyaları ve diğer metin dışı biçimler dahil) jetonlaştırılır. Gemini API tarafından işleme sırasında çok formatlı girişin jetonlaştırılmasıyla ilgili aşağıdaki üst düzey önemli noktaları göz önünde bulundurun:

Gemini 2.0 ile her iki boyutu da <=384 piksel olan görüntü girişleri 258 jeton olarak sayılır. Bir veya iki boyutta daha büyük olan resimler, gerektiğinde 768x768 piksellik parçalar halinde kırpılır ve ölçeklendirilir. Bu parçaların her biri 258 jeton olarak sayılır. Gemini 2.0'dan önce, görsellerde sabit 258 jeton kullanılıyordu.

Video ve ses dosyaları, aşağıdaki sabit oranlarda jetonlara dönüştürülür: Video: Saniyede 263 jeton, ses: saniyede 32 jeton.

Resim dosyaları
countTokens işlevini metin ve resim girişiyle çağırırsanız yalnızca girişte (totalTokens) metin ve resmin birleştirilmiş jeton sayısını döndürür. İsteklerinizin boyutunu kontrol etmek için generateContent işlevini çağırmadan önce bu çağrıyı yapabilirsiniz. İsteğe bağlı olarak metin ve dosya için ayrı ayrı countTokens işlevini de çağırabilirsiniz.

Diğer bir seçenek de generateContent numaralı telefonu arayıp usageMetadata özelliğini response nesnesinde kullanarak aşağıdakileri elde etmektir:

Girişin (promptTokenCount) ve çıkışın (candidatesTokenCount) ayrı jeton sayıları
Hem girişte hem de çıkışta toplam jeton sayısı (totalTokenCount)
Not: File API kullanılarak yüklenen bir dosyayı kullanırsanız veya dosyayı satır içi veri olarak sağlarsanız aynı jeton sayısını elde edersiniz.
File API'den yüklenen bir görüntünün kullanıldığı örnek:


// Make sure to include the following import:
// import {GoogleGenAI} from '@google/genai';
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const prompt = "Tell me about this image";
const organ = await ai.files.upload({
  file: path.join(media, "organ.jpg"),
  config: { mimeType: "image/jpeg" },
});

const countTokensResponse = await ai.models.countTokens({
  model: "gemini-2.0-flash",
  contents: createUserContent([
    prompt,
    createPartFromUri(organ.uri, organ.mimeType),
  ]),
});
console.log(countTokensResponse.totalTokens);

const generateResponse = await ai.models.generateContent({
  model: "gemini-2.0-flash",
  contents: createUserContent([
    prompt,
    createPartFromUri(organ.uri, organ.mimeType),
  ]),
});
console.log(generateResponse.usageMetadata);

Resmi satır içi veri olarak sağlayan örnek:


// Make sure to include the following import:
// import {GoogleGenAI} from '@google/genai';
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const prompt = "Tell me about this image";
const imageBuffer = fs.readFileSync(path.join(media, "organ.jpg"));

// Convert buffer to base64 string.
const imageBase64 = imageBuffer.toString("base64");

// Build contents using createUserContent and createPartFromBase64.
const contents = createUserContent([
  prompt,
  createPartFromBase64(imageBase64, "image/jpeg"),
]);

const countTokensResponse = await ai.models.countTokens({
  model: "gemini-2.0-flash",
  contents: contents,
});
console.log(countTokensResponse.totalTokens);

const generateResponse = await ai.models.generateContent({
  model: "gemini-2.0-flash",
  contents: contents,
});
console.log(generateResponse.usageMetadata);

Video veya ses dosyaları
Ses ve video, aşağıdaki sabit oranlarda jetonlara dönüştürülür:

Video: Saniyede 263 jeton
Ses: Saniyede 32 jeton
countTokens işlevini metin ve video/ses girişiyle çağırırsanız yalnızca girişteki metin ve video/ses dosyasının birleştirilmiş jeton sayısını döndürür (totalTokens). İsteklerinizin boyutunu kontrol etmek için generateContent işlevini çağırmadan önce bu işlevi çağırabilirsiniz. İsterseniz metni ve dosyayı ayrı ayrı countTokens da çağırabilirsiniz.

Diğer bir seçenek de generateContent numaralı telefonu arayıp usageMetadata özelliğini response nesnesinde kullanarak aşağıdakileri elde etmektir:

Girişin (promptTokenCount) ve çıkışın (candidatesTokenCount) ayrı jeton sayıları
Hem girişte hem de çıkışta toplam jeton sayısı (totalTokenCount)
Not: File API kullanılarak yüklenen bir dosyayı kullanırsanız veya dosyayı satır içi veri olarak sağlarsanız aynı jeton sayısını elde edersiniz.

// Make sure to include the following import:
// import {GoogleGenAI} from '@google/genai';
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const prompt = "Tell me about this video";
let videoFile = await ai.files.upload({
  file: path.join(media, "Big_Buck_Bunny.mp4"),
  config: { mimeType: "video/mp4" },
});

// Poll until the video file is completely processed (state becomes ACTIVE).
while (!videoFile.state || videoFile.state.toString() !== "ACTIVE") {
  console.log("Processing video...");
  console.log("File state: ", videoFile.state);
  await sleep(5000);
  videoFile = await ai.files.get({ name: videoFile.name });
}

const countTokensResponse = await ai.models.countTokens({
  model: "gemini-2.0-flash",
  contents: createUserContent([
    prompt,
    createPartFromUri(videoFile.uri, videoFile.mimeType),
  ]),
});
console.log(countTokensResponse.totalTokens);

const generateResponse = await ai.models.generateContent({
  model: "gemini-2.0-flash",
  contents: createUserContent([
    prompt,
    createPartFromUri(videoFile.uri, videoFile.mimeType),
  ]),
});
console.log(generateResponse.usageMetadata);