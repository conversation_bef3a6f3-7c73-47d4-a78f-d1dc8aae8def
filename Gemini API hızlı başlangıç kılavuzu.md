Gemini API hızlı başlangıç kılavuzu

Bu hızlı başlangıç kılavuzunda, kitaplıklarımızı nasıl yükleyeceğiniz ve ilk Gemini API isteğinizi nasıl göndereceğiniz gösterilmektedir.

Başlamadan önce
Gemini API anahtarına ihtiyacınız vardır. Henüz edinmediyseniz Google AI Studio'da ücretsiz olarak alabilirsiniz.

Google GenAI SDK'yı yükleme
Python
JavaScript
Go
Java
Apps Komut Dosyası
Node.js v18+'ı kullanarak aşağıdaki npm komutunu kullanarak TypeScript ve JavaScript için Google Gen AI SDK'yı yükleyin:


npm install @google/genai
İlk isteğinizi gönderme
Gemini 2.5 Flash modelini kullanarak Gemini API'ye istek göndermek için generateContent yönteminin kullanıldığı bir örneği aşağıda bulabilirsiniz.

API anahtarınızı ortam değişkeni GEMINI_API_KEY olarak ayarlarsanız Gemini API kitaplıkları kullanılırken istemci tarafından otomatik olarak alınır. Aksi takdirde, istemciyi başlatırken API anahtarınızı bağımsız değişken olarak iletmeniz gerekir.

Gemini API dokümanlarındaki tüm kod örneklerinde GEMINI_API_KEY ortam değişkenini ayarladığınız varsayılır.

Python
JavaScript
Go
Java
Apps Komut Dosyası
REST

import { GoogleGenAI } from "@google/genai";

// The client gets the API key from the environment variable `GEMINI_API_KEY`.
const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "Explain how AI works in a few words",
  });
  console.log(response.text);
}

main();
"Düşünme" özelliği, birçok kod örneğimizde varsayılan olarak açıktır.
Bu sitedeki birçok kod örneğinde, yanıt kalitesini artırmak için varsayılan olarak "düşünme" özelliği etkinleştirilmiş olan Gemini 2.5 Flash modeli kullanılmaktadır. Bunun yanıt süresini ve jeton kullanımını artırabileceğini unutmayın. Hıza öncelik veriyorsanız veya maliyetleri en aza indirmek istiyorsanız aşağıdaki örneklerde gösterildiği gibi düşünme bütçesini sıfıra ayarlayarak bu özelliği devre dışı bırakabilirsiniz. Daha fazla ayrıntı için düşünme kılavuzuna bakın.

Not: Düşünme özelliği yalnızca Gemini 2.5 serisi modellerde kullanılabilir ve Gemini 2.5 Pro'da devre dışı bırakılamaz.
Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "Explain how AI works in a few words",
    config: {
      thinkingConfig: {
        thinkingBudget: 0, // Disables thinking
      },
    }
  });
  console.log(response.text);
}

await main();