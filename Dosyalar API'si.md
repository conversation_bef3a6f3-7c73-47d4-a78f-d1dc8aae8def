gönderinDosyalar API'si

Gemini yapay zeka modeli ailesi; metin, resim ve ses gibi çeşitli giriş verilerini işlemek için geliştirilmiştir. Bu modeller birden fazla veri türünü veya modunu işleyebildiğinden Gemini modellerine çok formatlı modeller denir veya çok formatlı özelliklere sahip oldukları açıklanır.

Bu kılavuzda, Files API'yi kullanarak medya dosyalarıyla nasıl çalışacağınız gösterilmektedir. Ses dosyaları, resimler, videolar, dokümanlar ve diğer desteklenen dosya türleri için temel işlemler aynıdır.

Dosya istemiyle ilgili rehberlik için Dosya istemi kılavuzu bölümüne göz atın.

Dosya yükleyin
Medya dosyası yüklemek için Files API'yi kullanabilirsiniz. Toplam istek boyutu (dos<PERSON><PERSON>, metin istemi, sistem talimatları vb. dahil) 20 MB'tan büyük olduğunda her zaman Files API'yi kullanın.

Aşağıdaki kod, bir dosyayı yükler ve ardından generateContent çağrısında dosyayı kullanır.

Python
JavaScript
Go
REST

import {
  GoogleGenAI,
  createUserContent,
  createPartFromUri,
} from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const myfile = await ai.files.upload({
    file: "path/to/sample.mp3",
    config: { mimeType: "audio/mpeg" },
  });

  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: createUserContent([
      createPartFromUri(myfile.uri, myfile.mimeType),
      "Describe this audio clip",
    ]),
  });
  console.log(response.text);
}

await main();
Dosyanın meta verilerini alma
files.get işlevini çağırarak API'nin yüklenen dosyayı başarıyla depoladığını doğrulayabilir ve dosyanın meta verilerini alabilirsiniz.

Python
JavaScript
Go
REST

const myfile = await ai.files.upload({
  file: "path/to/sample.mp3",
  config: { mimeType: "audio/mpeg" },
});

const fileName = myfile.name;
const fetchedFile = await ai.files.get({ name: fileName });
console.log(fetchedFile);
Yüklenen dosyaları listeleme
Files API'yi kullanarak birden fazla dosya yükleyebilirsiniz. Aşağıdaki kod, yüklenen tüm dosyaların listesini alır:

Python
JavaScript
Go
REST

const listResponse = await ai.files.list({ config: { pageSize: 10 } });
for await (const file of listResponse) {
  console.log(file.name);
}
Yüklenen dosyaları silme
Dosyalar 48 saat sonra otomatik olarak silinir. Yüklenen bir dosyayı manuel olarak da silebilirsiniz:

Python
JavaScript
Go
REST

const myfile = await ai.files.upload({
  file: "path/to/sample.mp3",
  config: { mimeType: "audio/mpeg" },
});

const fileName = myfile.name;
await ai.files.delete({ name: fileName });
Kullanım bilgileri
Dosyalar API'sini kullanarak medya dosyalarını yükleyebilir ve bu dosyalarla etkileşim kurabilirsiniz. Files API, proje başına 20 GB'a kadar dosya depolamanıza olanak tanır. Dosya başına maksimum boyut 2 GB'tır. Dosyalar 48 saat boyunca saklanır. Bu süre zarfında, dosyalarla ilgili meta verileri almak için API'yi kullanabilirsiniz ancak dosyaları indiremezsiniz. Files API, Gemini API'nin kullanılabildiği tüm bölgelerde ücretsiz olarak kullanılabilir.

Dosya istemi stratejileri
Bu bölümde, Gemini API için istemlerle medya dosyalarını kullanmayla ilgili rehberlik ve en iyi uygulamalar yer almaktadır.

İstemlerinizde çeşitli veri türlerini kullanabilmek, Gemini API ile hangi görevleri yapabileceğiniz konusunda daha fazla esneklik sağlar. Örneğin, modele lezzetli bir yemeğin fotoğrafını gönderip yemekle ilgili kısa bir blog yazmasını isteyebilirsiniz.

İstem	Yanıt
Bu resme dayalı olarak kısa ve ilgi çekici bir blog yayını yaz. Fotoğraftaki yemeğin açıklamasını içermeli ve yemek hazırlama yolculuğumdan bahsetmelidir.


Çerezler	
Yemek hazırlığı, hem zamandan hem de paradan tasarruf etmenin harika bir yoludur ve daha sağlıklı beslenmenize de yardımcı olabilir. Bu yemek, önceden kolayca hazırlanabilen sağlıklı ve lezzetli bir yemeğin harika bir örneğidir.

Bu öğünde kahverengi pirinç, fırında sebzeler ve teriyaki soslu tavuk bulunur. Kahverengi pirinç, size uzun süreli enerji verecek sağlıklı ve kompleks bir karbonhidrattır. Kızartılmış sebzeler, günlük vitamin ve mineral ihtiyacınızı karşılamak için harika bir seçenektir. Teriyaki soslu tavuk ise lezzetli ve protein açısından zengin bir seçenektir.

Bu yemeği hazırlamak da çok kolaydır. Kahverengi pirinci pişirin, sebzeleri fırında kızartın ve teriyaki soslu tavuğu pişirin. Her şey piştikten sonra, yemek hazırlama kaplarına bölüp buzdolabında saklayın. Ardından, yoğun sabahlarınızda bir kap alıp çıkabilirsiniz.

Önceden kolayca hazırlanabilen sağlıklı ve lezzetli bir öğün arıyorsanız bu yemek harika bir seçenek. Besinler ve lezzetlerle dolu bu yemek, sizi tok ve memnun hissettirecek.

Sağlıklı ve lezzetli yemek hazırlama!

Medya dosyalarının kullanıldığı istemlerden istediğiniz çıktıyı almakta zorlanıyorsanız istediğiniz sonuçları elde etmenize yardımcı olabilecek bazı stratejiler vardır. Aşağıdaki bölümlerde, çok formatlı giriş kullanan istemleri iyileştirmeye yönelik tasarım yaklaşımları ve sorun giderme ipuçları verilmektedir.

Aşağıdaki en iyi uygulamaları izleyerek çok formatlı istemlerinizi iyileştirebilirsiniz:

İstem tasarımıyla ilgili temel bilgiler
Talimatlarınızda net olun: Yanlış yorumlamaya en az yer bırakacak şekilde net ve kısa talimatlar oluşturun.
İsteminize birkaç örnek ekleyin: Ne elde etmek istediğinizi göstermek için gerçekçi birkaç görev örneği kullanın.
Adım adım açıklama: Karmaşık görevleri yönetilebilir alt hedeflere ayırarak modele süreç boyunca rehberlik edin.
Çıkış biçimini belirtin: İsteminizde, çıkışın istediğiniz biçimde (ör. Markdown, JSON, HTML) olmasını isteyin.
Tek resimli istemlerde resminizi önce girin: Gemini, resim ve metin girişlerini herhangi bir sırada işleyebilse de tek resim içeren istemlerde, resim (veya video) metin isteminden önce yerleştirilirse daha iyi performans gösterebilir. Ancak, anlamlı olması için resimlerin metinlerle yoğun bir şekilde iç içe geçmesini gerektiren istemlerde en doğal olan sırayı kullanın.
Çok formatlı isteminizle ilgili sorunları giderme
Model, resmin ilgili bölümünden bilgi almıyorsa: İstemden bilgi almasını istediğiniz resmin hangi yönleriyle ilgili ipuçları verin.
Model çıkışı çok genelse (resim/video girişine yeterince uyarlanmamışsa): İstemin başında, görev talimatını vermeden önce modelden resimleri veya videoyu açıklamasını ya da resimde ne olduğunu belirtmesini isteyin.
Hangi bölümün başarısız olduğunu belirlemek için: Modelin ilk anlayışını ölçmek amacıyla modelden resmi açıklamasını veya gerekçesini açıklamasını isteyin.
İsteminiz halüsinasyon içeren içeriklerle sonuçlanıyorsa: Sıcaklık ayarını düşürmeyi veya modelden daha kısa açıklamalar istemeyi deneyin. Böylece modelin ek ayrıntılar eklemesi daha az olasıdır.
Örnekleme parametrelerini ayarlama: Modelin yaratıcılığını ayarlamak için farklı sıcaklık ayarları ve top-k seçimleriyle denemeler yapın.
Talimatlarınızda net olun
İstemler en iyi sonucu net ve ayrıntılı olduğunda verir. Aklınızda belirli bir çıktı varsa istediğiniz çıktıyı elde etmek için bu şartı isteme eklemeniz daha iyi olur.

Havaalanı panosunun yer aldığı bu resim için modelden yalnızca "bu resmi açıkla" demeniz genel bir açıklama oluşturabilir. Modelin resimdeki saati ve şehri ayrıştırmasını istiyorsanız bu isteği doğrudan isteminize ekleyebilirsiniz.

İstem	Model yanıtı

Bu resmi tanımla.

Resimde, havalimanındaki gelen ve giden uçuşlar tabelası gösteriliyor.

Güncellenen istem	İyileştirilmiş yanıt
Seyahat zaman çizelgesi
Bu resimde gösterilen havaalanı panosundaki saati ve şehri ayrıştırarak listeleyin.

10:50 Moskova
11:05 Edinburgh
11:05 Londra
11:10 Bükreş
11:30 Kiev
11:35 Dublin
11:45 East Midlands
12:15 Sofya
12:30 Londra
12:30 Newcastle
12:40 St Petersburg
12:40 Londra
12:45 Manchester
Birkaç örnek ekleyin
Gemini modeli, istediğiniz çıkışı anlamak için örnek olarak kullanabileceği birden fazla giriş kabul edebilir. Bu örnekleri eklemek, modelin kalıpları belirlemesine ve verilen resimler ile yanıtlar arasındaki ilişkiyi yeni örneğe uygulamasına yardımcı olabilir. Bu, "az görevli" öğrenme olarak da adlandırılır.

Aşağıdaki örnekte, ilk çıktı cümle şeklinde yazılmış ve ülkeyi (Brezilya) de içeriyor. Çıktının farklı bir biçimde veya tarzda olmasını ve yalnızca şehri (ülkeyi değil) istiyorsunuz. İsteminize birkaç görevli örnek eklemek, modelin istediğiniz şekilde yanıt vermesini sağlayabilir.

İstem	Model yanıtı
Seyahat zaman çizelgesi
Simge yapıyla birlikte şehri belirleyin.

Bu simge yapı, Brezilya'nın Rio de Janeiro şehrindeki Kurtarıcı İsa heykelidir.

Güncellenen istem	İyileştirilmiş yanıt
Simge yapıyla birlikte şehri belirleyin.

colosseum	
Şehir: Roma, simge yapı: Kolezyum.

Yasak Şehir	
city: Beijing, landmark: Forbidden City

forbidden-city	
city: Rio de Janeiro, landmark: Christ the Redeemer statue

Adım adım açıklama
Hem görsel anlayış hem de muhakeme gerektiren görevler gibi karmaşık görevleri daha küçük ve basit adımlara bölmek faydalı olabilir. Alternatif olarak, isteminizde doğrudan modelden "adım adım düşünmesini" istemek de etkili olabilir.

İstem	Model yanıtı

Tuvalet kağıdım ne zaman bitecek?

Yakında yalnızca 3 film rulosu hakkınız kalacak.

Güncellenen istem	İyileştirilmiş yanıt


1. Öncelikle bu resimde kaç rulo tuvalet kağıdı olduğunu sayın.
2. Ardından, bir kişinin günde ne kadar tuvalet kağıdı kullandığını belirleyin.
3. Bu tuvalet kağıdı rulolarının ne kadar süre dayanacağını hesaplayın.

1. Bu resimde 3 rulo tuvalet kağıdı var.
2. Tipik bir kişi günde yaklaşık 20 yaprak tuvalet kağıdı kullanır.
3. Her ruloda 200 yaprak varsa her rulo yaklaşık 10 gün dayanır. Bu nedenle, 3 rulo yaklaşık bir ay dayanır.
Matematik problemleri veya diğer sözel problemler, modelden adım adım düşünmesini istemek için idealdir.

İstem	Yanıt


Dizideki 4. terim nedir?

-135

Modelin yanıtı yanlış. Bunu iyileştirmenin bazı yolları, modelden isteme yanıt vermeden önce problemi ayrıştırmasını istemektir.

İstem	Yanıt


Önce resimdeki formülü ayrıştırın.

Ardından formüle göre "Dizideki 4. terim nedir?" sorusunu yanıtlayın.

Sonucu genişletmek için tıklayın
Bu model yanıtı doğru. Yanıtı daha da kolaylaştırmak için modele adım adım düşünmesini söyleyebilirsiniz.

Güncellenen istem	İyileştirilmiş yanıt

Dizideki 4. terim nedir? Adım adım düşünün.

İlk terim 15'tir.

İkinci terim b(1) * (-3) = 15 * (-3) = -45 olur.

Üçüncü terim b(2) * (-3) = -45 * (-3) = 135'tir.

Dördüncü terim b(3) * (-3) = 135 * (-3) = -405'tir.

Çıkış biçimini belirtme
Model çıkışının belirli bir biçimde (ör. Markdown, JSON, HTML) olması gerektiğinde, özellikle model çıkışının sonraki bir görev tarafından alınması gerektiğinde yaygın bir sorun ortaya çıkar. İstemde modeli bu biçimde çıkış üretmeye yönlendirerek bu biçimde çıkış üretmeyi deneyebilirsiniz. Aşağıda iki örnek verilmiştir:

İstem	Model yanıtı

Bu resimdeki tabloyu Markdown biçiminde ayrıştır

| Konu | Durum | Saat | Başlangıç tarihi | Son tarih |

| ------- | ------ | ---- | ---------- | ------ |

| Matematik | Tamamlandı | 1,5 saat | 29.11.2023 | 01.12.2023 |

| Coğrafya | Tamamlandı | 30 dakika | 2.12.2023 | 4.12.2023 |

| Fizik | Devam ediyor | 2 saat | 3.12.2023 | 5.12.2023 |

| Biyoloji | Başlamadı | 6 saat | 02.11.2016 | 06.12.2023 |

İstem	Model yanıtı

Aşağıdaki tüm özelliklerin listesini sağlayın:

JSON biçiminde malzemeler, mutfak türü, vejetaryen olup olmadığı


```json

{

"ingredients": [

"rice",

"shrimp",

"clams",

"mussels",

"peas",

"tomatoes",

"onions",

"garlic",

"olive oil",

"paprika",

"salt",

"pepper"

],

"type of cuisine": "Spanish",

"vegetarian": false

}

```

Tek resim içeren istemlerde resminizi ilk sıraya yerleştirin
Gemini, istemdeki resimleri ve metinleri herhangi bir sırada yorumlayabilir ancak metin isteminden önce tek bir resim yerleştirmek daha iyi sonuçlar verebilir. Aşağıdaki örneklerde, her istemde önce resmin, ardından metnin geldiğini göreceksiniz.

Çok formatlı isteminizle ilgili sorunları giderme
Faydalı bir yanıt alamıyorsanız isteminizle ilgili sorunları gidermeniz gerekebilir. Deneyebileceğiniz birkaç strateji:

Model, resmin ilgili bölümünden bilgi almıyorsa
Daha ayrıntılı bir yanıt almak için istemin, yanıtını oluştururken hangi resim öğelerinden bilgi almasını istediğinizi belirtebilirsiniz.

İstem	Model yanıtı

Bu bezler bir bebeğe kaç gün yeter?

Bezler bitmeden 198 gün boyunca kullanılabilir.

Güncellenen istem	İyileştirilmiş yanıt

Bu bezler bitmeden ne kadar süre dayanır?

Çocuğun yaşını belirlemek için kutuda gösterilen ağırlığı ve kutudaki toplam bebek bezi sayısını kullanın.

Toplam sayıyı, çocuğun günde kullandığı bez sayısına bölün.

Kutuda 198 adet bez bulunur. Kutuda, bezlerin 10-17 kg ağırlığındaki çocuklar için olduğu belirtiliyor.

Bu ağırlık aralığındaki ortalama bir çocuk muhtemelen 1-2 yaşlarındadır.

2 yaşındaki bir çocuk genellikle günde 6 bez kullanır. Bu nedenle, bezler yaklaşık 33 gün (198/6) dayanır.

Model çıkışı çok genel ve resim girişine yeterince uyarlanmamışsa
Modelin yanıtını resimlere göre uyarlamasına yardımcı olmak için akıl yürütme görevini gerçekleştirmeden önce resimleri açıklamasını isteyin.

İstem	Model yanıtı
   
Bu resimlerin ortak noktası nedir?

Dört resmin tamamı manzara fotoğrafı örneğidir.

Güncellenen istem	İyileştirilmiş yanıt
   
Öncelikle her resimde ne olduğunu ayrıntılı olarak açıklayın. Bu resimlerin ortak noktası nedir?

İlk resimde, bir kişinin bavul hazırladığı gösteriliyor. İkinci resimde, bir alışveriş merkezindeki boş koridor gösteriliyor. Üçüncü resimde bir rulo tuvalet kağıdı gösteriliyor. Dördüncü resimde karanlık ve fırtınalı bir deniz gösteriliyor.

Bu resimlerin ortak özelliği, hepsinin geçiş alanlarına örnek olmasıdır. Liminal alanlar, iki yer veya durum arasında kalan yerler ya da durumlardır. Genellikle belirsizlik veya muğlaklık hissiyle karakterize edilirler.

İlk resimde, kişinin evde ve seyahatte olduğu anlar gösteriliyor. İkinci resimde, iki mağaza arasındaki koridor gösteriliyor. Üçüncü resimde, banyoyu temsil edebilecek tuvalet kağıdı gösteriliyor. Banyo, özel ve kamusal alanlar arasında geçiş yapılan bir yerdir. Dış dünyaya girerken veya dış dünyadan çıkarken kendimizi hazırladığımız bir yerdir.

Dördüncü resimde, deniz sakin ve fırtınalı arasında bir durumda gösteriliyor.

Geçiş alanları hem heyecan verici hem de rahatsız edici olabilir. Yeni başlangıçların veya sonların yeri olabilirler. Bunlar, düşünme veya harekete geçme yeri olabilir.

Ayrıca, modelden resimdeki içeriğe açıkça atıfta bulunmasını da isteyebilirsiniz.

Güncellenen istem	İyileştirilmiş yanıt
   
Bu resimlerin ortak noktası nedir? Yanıttaki resimlerin içeriğine atıfta bulun.

Resimlerin tamamı seyahatle ilgili. İlk resimde bavul hazırlayan bir kişi, ikinci resimde bir alışveriş merkezindeki boş koridor, üçüncü resimde bir rulo tuvalet kağıdı, dördüncü resimde ise karanlık ve fırtınalı bir deniz gösteriliyor.

Bu resimlerin tümü, insanlar seyahat ederken ihtiyaç duyduğu veya kullandığı şeyler olduğu için seyahatle ilgilidir. İlk resimde, bir valizi hazırlayan bir kişi gösteriliyor. Bu, insanların seyahate çıkarken yaptığı bir şeydir. İkinci resimde, insanların seyahatleri için eşya satın almak üzere sık sık gittiği bir yer olan alışveriş merkezindeki boş bir koridor gösteriliyor. Üçüncü resimde, insanların seyahat ederken sıkça kullandığı bir ürün olan tuvalet kağıdı rulosu gösteriliyor. Dördüncü resimde, insanların seyahat ederken sıkça karşılaştığı bir durum olan karanlık ve fırtınalı bir deniz gösteriliyor.

Aynı ipuçları, resimlerden elde edilen reklam öğesi çıktısı için de geçerlidir. Tavşanın yer aldığı aşağıdaki örnekte, modelden resimdeki unsurları dahil etmesini açıkça istemek çıkışı iyileştirebilir.

İstem	Model yanıtı

Bu resim hakkında bir şiir yazabilir misin?

Küçük tavşan, yumuşacık ve kahverengi,
Tarlada zıplıyor, yukarı ve aşağı.
Kulakların uzun, kuyruğun küçük,
En sevimli yaratık sensin.

Yeşil ve tatlı çimleri yersin,
Beni görünce de kaçarsın.
Ama her zaman arkadaşın olacağım,
Küçük tavşan, sonuna kadar.

Güncellenen istem	İyileştirilmiş yanıt

Bu resim hakkında açıklayıcı ve etkileyici bir şiir yazabilir misin? Şiire konumu da ekle.

Çayırda küçük bir tavşan,
Kulakları çok uzun, çok keskin.
Ne görüyorsun orada,
Uzakta, baktığın yerde?

Is it the ocean blue,
Sparkling in the sun's hue?
Ya da dalgaların çarpıp kükremesi,
Bayıldığınız bir ritim?

Küçük tavşan, doğruyu söyle bana,
İlginizi bu kadar çok çeken şey ne?
Tuzlu esinti mi,
Yoksa martıların çığlıkları mı hoşunuza gidiyor?

Belki de her şeyin büyüklüğüdür,
Dünya hem çok büyük hem de çok küçük.
Hayallerin dolaşabileceği,
Maceraların tohumlarının atılabileceği bir yer.

Küçük tavşan, ne hayal ettiğini merak ediyorum,
Çimlerin üzerinde otururken, çok sakinsin.
Derinlikleri keşfetmek mi istersin,
Yoksa karada kalıp zıplamak mı?

Ne olursa olsun, küçük tavşan,
Merak kıvılcımını parlak bir şekilde yakmaya devam et.
Çünkü hayallerinizde ve arzularınızda,
Yaratılmayı bekleyen bir dünya var.

İstemin hangi bölümünün başarısız olduğunu belirleme
Bir istemin başarısız olmasının nedeni, modelin görüntüyü anlamaması mı yoksa görüntüyü anlamasına rağmen doğru akıl yürütme adımlarını uygulamaması mı, bunu anlamak zor olabilir. Bu nedenleri netleştirmek için modele resimde ne olduğunu açıklamasını isteyin.

Aşağıdaki örnekte, model çayla birlikte şaşırtıcı görünen bir atıştırmalıkla (ör. patlamış mısır) yanıt verirse önce modelin resimde çay olduğunu doğru şekilde tanıyıp tanımadığını belirlemek için sorun giderme işlemi yapabilirsiniz.

İstem	Sorun giderme istemi

Bununla iyi gidecek, 1 dakikada hazırlayabileceğim bir atıştırmalık önerir misin?


Bu resimde ne olduğunu açıkla.

Diğer bir strateji ise modelden gerekçesini açıklamasını istemektir. Bu, muhakemenin hangi kısmının (varsa) bozulduğunu daraltmanıza yardımcı olabilir.

İstem	Sorun giderme istemi

Bununla iyi gidecek, 1 dakikada hazırlayabileceğim bir atıştırmalık önerir misin?


Bununla iyi gidecek, 1 dakikada hazırlayabileceğim bir atıştırmalık önerir misin? Lütfen nedeniyle birlikte açıklayın.