Hızlı tasarım stratejileri

İstem tasarımı, dil modelinden doğru ve yüksek kaliteli yanıtlar alınmasını sağlayan istemler veya doğal dil istekleri oluşturma sürecidir.

<PERSON><PERSON> say<PERSON><PERSON>, Gemini yapay zeka modellerinden en iyi şekilde yararlanmak için istem tasarlamaya başlamanıza yardımcı olacak temel kavramlar, stratejiler ve en iyi uygulamalar tanıtılmaktadır.

Konuya özel istem kılavuzları
Daha spesifik istem stratejileri mi arıyorsunuz? Aşağıdaki konularda diğer istem kılavuzlarımıza göz atın:

Me<PERSON>a dosyalarıyla istem oluşturma
Görüntü üretme için istem girme
Video üretme için istem girme
Google AI Studio'da, bu kılavuzda paylaşılan birçok kavramı etkileşimli olarak sergilemek için tasarlanmış bir istem galerisi de bulunur.

Net ve spesifik talimatlar
Model davranışını özelleştirmenin etkili ve verimli bir yolu, modele net ve spesifik talimatlar vermektir. Talimatlar soru, adım adım görevler şeklinde olabilir veya kullanıcının deneyimini ve düşünce tarzını haritalandırmak kadar karmaşık olabilir.

Giriş
Giriş, istemde modelin yanıt vermesini istediğiniz zorunlu metindir. Girişler; modelin yanıtladığı bir soru (soru girişi), modelin gerçekleştirdiği bir görev (görev girişi), modelin üzerinde işlem yaptığı bir varlık (varlık girişi) veya modelin tamamladığı ya da devam ettirdiği kısmi giriş (tamamlama girişi) olabilir.

Giriş türü	İstem	Oluşturulan çıktı
Soru	

What's a good name for a flower shop that specializes in selling bouquets of
dried flowers? Create a list of 5 options with just the names.

Here are 10 names for a flower shop specializing in dried flowers:

1. Everlasting Blooms
2. Dried & Delightful
3. The Petal Preserve
4. Whispers of Wildflowers
5. Forever Blooms
Görev	
Give me a simple list of just the things that I must bring on a camping trip. The list should have 5 items.
1. **Tent:** Your shelter from the elements.
2. **Sleeping Bag:** Stay warm and comfortable.
3. **Sleeping Pad:** Insulation and cushioning for a good night's sleep.
4. **Headlamp or Flashlight:** Light for nighttime activities.
5. **First Aid Kit:** Be prepared for minor injuries.
Varlık	

Classify the following items as [large, small]:
Elephant
Mouse
Snail

Here's the classification:

* **Elephant:** Large
* **Mouse:** Small
* **Snail:** Small
Kısmi giriş tamamlama
Üretken dil modelleri, gelişmiş bir otomatik tamamlama aracı gibi çalışır. Kısmi içerik sağladığınızda model, içeriğin geri kalanını veya içeriğin devamı olduğunu düşündüğü kısmı yanıt olarak verebilir. Bunu yaparken örnek veya bağlam eklerseniz model bu örnekleri ya da bağlamı dikkate alabilir.

Aşağıdaki örnekte, talimat ve öğe girişi içeren bir istem sağlanmaktadır:

İstem:
For the given order, return a JSON object that has the fields cheeseburger, hamburger, fries, or
drink, with the value being the quantity.

Order: A burger and a drink.
Yanıt:
{
"cheeseburger": 0,
"hamburger": 1,
"fries": 0,
"drink": 1
}
(gemini-2.5-flash)
Model, isteneni yapsa da talimatları doğal dilde yazmak bazen zor olabilir ve modelin yorumuna çok şey bırakır. Örneğin, bir restoranın menüsünde birçok öğe olabilir. JSON yanıtının boyutunu küçültmek için sipariş edilmeyen öğeleri atlamak isteyebilirsiniz. Bu durumda, bir örnek ve yanıt öneki verebilir ve modelin bunu tamamlamasına izin verebilirsiniz:

İstem:
Valid fields are cheeseburger, hamburger, fries, and drink.
Order: Give me a cheeseburger and fries
Output:
```
{
"cheeseburger": 1,
"fries": 1
}
```
Order: I want two burgers, a drink, and fries.
Output:
Yanıt:
```
{
"hamburger": 2,
"drink": 1,
"fries": 1
}
```
(gemini-2.5-flash)
"Cheeseburger"ın siparişin bir parçası olmadığı için çıkıştan nasıl hariç tutulduğuna dikkat edin.

Sınırlamalar
İstemi okuma veya yanıt oluşturma ile ilgili kısıtlamaları belirtin. Modele ne yapıp ne yapmaması gerektiğini söyleyebilirsiniz. Örneğin, istemde özetin ne kadar uzun olmasını istediğinize dair bir kısıtlama belirtebilirsiniz:

İstem:

Summarize this text in one sentence:
Text: A quantum computer exploits quantum mechanical phenomena to perform calculations exponentially
faster than any modern traditional computer. At very tiny scales, physical matter acts as both
particles and as waves, and quantum computing uses specialized hardware to leverage this behavior.
The operating principles of quantum devices is beyond the scope of classical physics. When deployed
at scale, quantum computers could be used in a wide variety of applications such as: in
cybersecurity to break existing encryption methods while helping researchers create new ones, in
meteorology to develop better weather forecasting etc. However, the current state of the art quantum
computers are still largely experimental and impractical.
Yanıt:

Exploiting quantum mechanical phenomena, quantum computers can perform calculations exponentially faster than traditional computers for potential applications like cybersecurity and meteorology, although they are currently largely experimental and impractical.
(gemini-2.5-flash)
Yanıt biçimi
Yanıtın biçimini belirten talimatlar verebilirsiniz. Örneğin, yanıtın tablo, madde işaretli liste, kısa açıklama, anahtar kelime, cümle veya paragraf olarak biçimlendirilmesini isteyebilirsiniz. Aşağıdaki sistem talimatı, modele yanıtında daha sohbet tarzında olmasını söylüyor:

Sistem talimatı
All questions should be answered comprehensively with details, unless the user requests a concise response specifically.
İstem
What is a smart way to make a business that sells DVD's in 2025?
Yanıt:
Starting a business selling DVDs in 2025 presents significant challenges due to the overwhelming dominance of digital streaming and on-demand services; a "smart" approach would therefore require a highly specialized niche, a strong value proposition beyond just the disc, and likely a focus on community or collector markets rather than mainstream consumption.

Here's a breakdown of what a smart DVD business in 2025 would likely entail:

1. Extreme Niche Specialization: You cannot compete with streaming libraries. Your business must focus on what digital doesn't offer easily or at all. This could include:

* Out-of-Print & Rare Titles: Sourcing films or TV shows that have never been released digitally or are currently unavailable on major streaming platforms.
....
(gemini-2.5-flash)
Tamamlama stratejisiyle yanıtları biçimlendirme
Tamamlama stratejisi, yanıtın biçimlendirilmesine de yardımcı olabilir. Aşağıdaki örnekte, modelden bir deneme taslağı oluşturması isteniyor:

İstem:
Create an outline for an essay about hummingbirds.
Yanıt:
Here is a possible outline for an essay about hummingbirds:

I. Introduction
A. Hook: Start with a captivating fact or description about hummingbirds (e.g., their speed, iridescent colors, or tiny size).
B. Background: Briefly introduce hummingbirds – where they are found (Americas), their reputation as unique birds.
C. Thesis Statement: State the main point of the essay, which could be about the remarkable adaptations, behaviors, and ecological importance of hummingbirds.

II. Appearance and Identification
.....
(gemini-2.5-flash)
İstemde, ana hat için biçim belirtilmedi ve model sizin için bir biçim seçti. Modelin belirli bir biçimde ana hat döndürmesini sağlamak için ana hat başlangıcını temsil eden metin ekleyebilir ve başlattığınız düzene göre modeli tamamlamasına izin verebilirsiniz.

İstem:
Create an outline for an essay about hummingbirds.
I. Introduction
*
Yanıt:
Okay, here is an outline for an essay about hummingbirds, starting from the introduction you provided:

I. Introduction
* Hook: Start with a captivating fact or description about hummingbirds (e.g., their speed, iridescent colors, or tiny size).
* Background: Briefly introduce hummingbirds – where they are found (Americas), their reputation as unique birds, and their general characteristics.
* Thesis Statement: State the main point of the essay, focusing on the remarkable adaptations, behaviors, and ecological significance that make hummingbirds extraordinary.
.....
(gemini-2.5-flash)
Sıfır görevli ve çok görevli istemler
İstemde, modele doğru yanıtın nasıl göründüğünü gösteren örnekler ekleyebilirsiniz. Model, örneklerdeki kalıpları ve ilişkileri belirlemeye çalışır ve yanıt oluştururken bunları uygular. Birkaç örnek içeren istemlere çok görevli istem, örnek içermeyen istemlere ise sıfır görevli istem adı verilir. Çok görevli istemler genellikle model yanıtlarının biçimlendirmesini, ifade tarzını, kapsamını veya genel kalıbını düzenlemek için kullanılır. Modelin odak noktasını daraltmasına ve daha doğru sonuçlar üretmesine yardımcı olmak için spesifik ve çeşitli örnekler kullanın.

İstemlerinize her zaman birkaç görevli örnek eklemenizi öneririz. Birkaç görev örneği içermeyen istemlerin etkili olma olasılığı daha düşüktür. Hatta örnekleriniz, söz konusu görevi gösterme konusunda yeterince netse isteminizdeki talimatları kaldırabilirsiniz.

Aşağıdaki sıfır görevli istem, modelden en iyi açıklamayı seçmesini ister.

İstem:
Please choose the best explanation to the question:

Question: How is snow formed?
Explanation1: Snow is formed when water vapor in the air freezes into ice
crystals in the atmosphere, which can combine and grow into snowflakes as they
fall through the atmosphere and accumulate on the ground.
Explanation2: Water vapor freezes into ice crystals forming snow.
Answer:
Yanıt:
Explanation1 is the better explanation because it provides more detail on the
process, including how ice crystals combine and grow into snowflakes as they
fall through the atmosphere.
(gemini-2.5-flash)
Kullanım alanınızda modelin kısa yanıtlar üretmesi gerekiyorsa isteme kısa yanıtları tercih eden örnekler ekleyebilirsiniz.

Aşağıdaki istemde, daha kısa açıklamalara öncelik verildiğini gösteren iki örnek yer almaktadır. Yanıtta, örneklerin modeli daha önce yaptığı gibi daha uzun açıklama (Explanation1) yerine daha kısa açıklamayı (Explanation2) seçmeye yönlendirdiği görülüyor.

İstem:
Below are some examples showing a question, explanation, and answer format:

Question: Why is the sky blue?
Explanation1: The sky appears blue because of Rayleigh scattering, which causes
shorter blue wavelengths of light to be scattered more easily than longer red
wavelengths, making the sky look blue.
Explanation2: Due to Rayleigh scattering effect.
Answer: Explanation2

Question: What is the cause of earthquakes?
Explanation1: Sudden release of energy in the Earth's crust.
Explanation2: Earthquakes happen when tectonic plates suddenly slip or break
apart, causing a release of energy that creates seismic waves that can shake the
ground and cause damage.
Answer: Explanation1

Now, Answer the following question given the example formats above:

Question: How is snow formed?
Explanation1: Snow is formed when water vapor in the air freezes into ice
crystals in the atmosphere, which can combine and grow into snowflakes as they
fall through the atmosphere and accumulate on the ground.
Explanation2: Water vapor freezes into ice crystals forming snow.
Answer:
Yanıt:
Answer: Explanation2
(gemini-2.5-flash)
İdeal örnek sayısı
Gemini gibi modeller, genellikle birkaç örnek kullanarak kalıpları algılayabilir. Ancak en iyi sonuçları elde etmek için istemde sağlanan örnek sayısıyla denemeler yapmanız gerekebilir. Aynı zamanda, çok fazla örnek eklerseniz model, yanıtı örneklere aşırı uydurmaya başlayabilir.

Kalıplar ve anti kalıplar
Modele izlemesi gereken bir kalıbı göstermek için örnekler kullanmak, kaçınması gereken bir anti-kalıbı göstermek için örnekler kullanmaktan daha etkilidir.

⛔ Olumsuz kalıp:

Örnek
Don't end haikus with a question:
Haiku are fun
A short and simple poem
Don't you enjoy them?

✅ Olumlu kalıp:

Örnek
Always end haikus with an assertion:
Haiku are fun
A short and simple poem
A joy to write

Tutarlı format
İstenmeyen biçimlerde yanıtlar almamak için birden çok örnekteki yapının ve biçimlendirmenin aynı olduğundan emin olun. İstemlere az görevli örnekler eklemenin temel amaçlarından biri, modele yanıt biçimini göstermektir. Bu nedenle, özellikle XML etiketlerine, boşluklara, yeni satırlara ve örnek ayırıcılara dikkat ederek tüm örneklerde tutarlı bir biçim sağlamak çok önemlidir.

Bağlam bilgisi ekleme
Modelin gerekli tüm bilgilere sahip olduğunu varsaymak yerine, bir isteme talimatlar ve bilgiler ekleyerek modelin bir sorunu çözmesini sağlayabilirsiniz. Bu bağlamsal bilgiler, modelin istediğiniz işle ilgili kısıtlamaları ve ayrıntıları anlamasına yardımcı olur.

Aşağıdaki örnekte, modelden bir yönlendiriciyle ilgili sorun giderme talimatları vermesi isteniyor:

İstem:
What should I do to fix my disconnected wifi? The light on my Google
Wifi router is yellow and blinking slowly.
Yanıt:
A yellow blinking light on your Google Wifi router usually indicates a problem
with the internet connection. Here are some steps to troubleshoot and fix the
issue:
1. Restart your router and modem:
- Unplug your modem from the power outlet and wait for 30 seconds.
- Plug your modem back in and wait for it to fully boot up.
- Unplug your Google Wifi router and wait for 30 seconds.
- Plug your Google Wifi router back in and wait for it to fully boot up.
.....
(gemini-2.5-flash)
Yanıtta, yönlendiriciye veya LED gösterge ışıklarının durumuna özgü olmayan genel sorun giderme bilgileri yer alıyor.

Yanıtı belirli bir yönlendirici için özelleştirmek istiyorsanız isteme, yanıt verirken başvurması için yönlendiricinin sorun giderme kılavuzunu bağlam olarak ekleyebilirsiniz.

İstem:
Answer the question using the text below. Respond with only the text provided.
Question: What should I do to fix my disconnected wifi? The light on my Google Wifi router is yellow and blinking slowly.

Text:
Color: Slowly pulsing yellow
What it means: There is a network error.
What to do:
Check that the Ethernet cable is connected to both your router and your modem and both devices are turned on. You might need to unplug and plug in each device again.

Color: Fast blinking yellow
What it means: You are holding down the reset button and are factory resetting this device.
What to do:
If you keep holding down the reset button, after about 12 seconds, the light will turn solid yellow. Once it is solid yellow, let go of the factory reset button.

Color: Solid yellow
What it means: Router is factory resetting.
What to do:
This can take up to 10 minutes. When it's done, the device will reset itself and start pulsing white, letting you know it's ready for setup.

Color: Solid red
What it means: Something is wrong.
What to do:
Critical failure. Factory reset the router. If the light stays red, contact Wifi customer support.
Yanıt:
Check that the Ethernet cable is connected to both your router and your modem and both devices are turned on. You might need to unplug and plug in each device again.
(gemini-2.5-flash)
Önek ekleme
Ön ek, istem içeriğine eklediğiniz bir kelime veya kelime öbeğidir. Ön eki nereye yerleştirdiğinize bağlı olarak çeşitli amaçlara hizmet edebilir:

Giriş öneki: Giriş sinyallerine önek eklemek, girişin anlamsal olarak anlamlı kısımlarını modele ekler. Örneğin, "İngilizce:" ve "Fransızca:" önekleri iki farklı dili belirtir.
Çıkış öneki: Çıkış model tarafından oluşturulsa da isteme çıkış için bir önek ekleyebilirsiniz. Çıkış öneki, modele yanıt olarak ne beklendiği hakkında bilgi verir. Örneğin, "JSON:" çıkış öneki, çıkışın JSON biçiminde olması gerektiğini modele bildirir.
Önek örneği: Az görevli istemlerde örneklere önek eklemek, modelin çıkışı oluştururken kullanabileceği etiketler sağlar. Bu da çıkış içeriğinin ayrıştırılmasını kolaylaştırır.
Aşağıdaki örnekte, "Metin:" giriş öneki, "Yanıt:" ise çıkış önekidir.

İstem:
Classify the text as one of the following categories.
- large
- small
Text: Rhino
The answer is: large
Text: Mouse
The answer is: small
Text: Snail
The answer is: small
Text: Elephant
The answer is:
Yanıt:
The answer is: large
(gemini-2.5-flash)
İstemleri bileşenlere ayırma
Karmaşık istemler gerektiren kullanım alanlarında, öğeleri daha basit bileşenlere ayırarak modelin bu karmaşıklığı yönetmesine yardımcı olabilirsiniz.

Talimatları bölme: Tek bir istemde çok sayıda talimat vermek yerine her talimat için ayrı bir istem oluşturun. Kullanıcının girişine göre hangi istemin işleneceğini seçebilirsiniz.

Zincirleme istemler: Birden fazla sıralı adım içeren karmaşık görevler için her adımı istem olarak girin ve istemleri bir sırayla zincirleyin. Bu sıralı istem zincirinde, dizideki bir istemin çıkışı bir sonraki istemin girişi olur. Dizideki son istemin çıkışı, nihai çıkıştır.

Yanıtları toplama: Toplama, verilerin farklı bölümlerinde farklı paralel görevler gerçekleştirmek ve sonuçları toplayarak nihai çıktıyı oluşturmak istediğinizde kullanılır. Örneğin, modele verilerin ilk bölümünde bir işlem, verilerin geri kalanında başka bir işlem yapmasını ve sonuçları toplaması talimatını verebilirsiniz.

Model parametreleriyle denemeler yapma
Bir modele gönderdiğiniz her çağrı, modelin nasıl yanıt üreteceğini kontrol eden parametre değerleri içerir. Model, farklı parametre değerleri için farklı sonuçlar üretebilir. Görev için en iyi değerleri elde etmek amacıyla farklı parametre değerleriyle denemeler yapın. Farklı modeller için kullanılabilen parametreler farklı olabilir. En yaygın parametreler şunlardır:

Maksimum çıkış jetonu: Yanıtta oluşturulabilecek maksimum jeton sayısını belirtir. Bir jeton, yaklaşık dört karakterden oluşur. 100 jeton yaklaşık 60-80 kelimeye karşılık gelir.

Sıcaklık: Sıcaklık, jeton seçimindeki rastgelelik derecesini kontrol eder. Sıcaklık, topP ve topK uygulandığında gerçekleşen yanıt oluşturma sırasında örnekleme için kullanılır. Düşük sıcaklıklar, daha belirleyici veya daha az açık uçlu yanıt gerektiren istemler için tercih edilir. Yüksek sıcaklıklar ise daha çeşitli veya yaratıcı sonuçlar sunabilir. Sıcaklık 0 olduğunda her zaman en yüksek olasılığa sahip yanıt seçilir.

topK: topK parametresi, modelin çıkış için jetonları nasıl seçeceğini değiştirir. 1 topK, seçilen jetonun modelin kelime dağarcığındaki tüm jetonlar arasında en olası jeton olduğu anlamına gelir (açgözlü kod çözme olarak da adlandırılır). 3 topK ise bir sonraki jetonun sıcaklık kullanılarak en olası 3 jeton arasından seçildiği anlamına gelir. Her jeton seçimi adımında, en yüksek olasılıklara sahip topK jeton örneklenir. Jetonlar daha sonra topP'ye göre daha da filtrelenir ve son jeton, sıcaklık örneklemesi kullanılarak seçilir.

topP: topP parametresi, modelin çıkış için jetonları nasıl seçeceğini değiştirir. Olasılıklarının toplamı topP değerine eşit olana kadar en olasıdan en az olasıya doğru parçalar seçilir. Örneğin, A, B ve C jetonlarının olasılığı 0,3, 0,2 ve 0,1 ise ve topP değeri 0,5 ise model, sıcaklığı kullanarak sonraki jeton olarak A veya B'yi seçer ve C'yi aday olarak hariç tutar. Varsayılan topP değeri 0,95'tir.

stop_sequences: Modeli içerik üretmeyi durdurmaya yönlendirmek için durdurma sırası ayarlayın. Durdurma dizisi herhangi bir karakter dizisi olabilir. Oluşturulan içerikte görünebilecek karakter dizilerini kullanmaktan kaçının.

İstem tekrarlama stratejileri
İstemin tasarımı, istediğiniz yanıtı tutarlı bir şekilde almadan önce bazen birkaç yineleme gerektirebilir. Bu bölümde, istemlerinizi yineleme sırasında deneyebileceğiniz bazı yöntemler hakkında bilgi verilmektedir:

Farklı ifadeler kullanın: İstemlerinizde farklı kelimeler veya ifadeler kullanmak, aynı anlama gelseler bile modelden genellikle farklı yanıtlar almanızı sağlar. İsteminizden beklediğiniz sonuçları alamıyorsanız isteminizi yeniden ifade etmeyi deneyin.

Version 1:
How do I bake a pie?

Version 2:
Suggest a recipe for a pie.

Version 3:
What's a good pie recipe?

Benzer bir göreve geçin: Modelin bir görevle ilgili talimatlarınızı uygulamasını sağlayamıyorsanız aynı sonucu elde eden benzer bir görevle ilgili talimatlar vermeyi deneyin.

Bu istemde, modelden önceden tanımlanmış kategorileri kullanarak bir kitabı sınıflandırması isteniyor:

İstem:
Which category does The Odyssey belong to:
thriller
sci-fi
mythology
biography
Yanıt:
The Odyssey belongs to the category of **mythology**.

Here's why:

* **Mythology:** The Odyssey tells the story of Odysseus, a hero from Greek mythology, and his
journey home after the Trojan War. It features gods, monsters, and supernatural events common to
Greek mythology.
.....
(gemini-2.5-flash)
Yanıt doğru ancak model, seçeneklerin sınırları içinde kalmadı. Ayrıca, modelin tam cümleyle yanıt vermek yerine yalnızca seçeneklerden birini kullanarak yanıt vermesini istiyorsunuz. Bu durumda, talimatları çoktan seçmeli soru olarak yeniden ifade edebilir ve modelden bir seçenek belirlemesini isteyebilirsiniz.

İstem:
Multiple choice problem: Which of the following options describes the book The Odyssey?
Options:



thriller

sci-fi

mythology

biography
Yanıt:
The correct answer is mythology.
(gemini-2.5-flash)
İstem içeriğinin sırasını değiştirme: İstemdeki içeriğin sırası bazen yanıtı etkileyebilir. İçerik sırasını değiştirmeyi deneyin ve bunun yanıtı nasıl etkilediğini görün.


Version 1:
[examples]
[context]
[input]

Version 2:
[input]
[examples]
[context]

Version 3:
[examples]
[input]
[context]
Yedek yanıtlar
Yedek yanıt, istem veya yanıt bir güvenlik filtresini tetiklediğinde model tarafından döndürülen yanıttır. Yedek yanıta örnek olarak "Ben sadece bir dil modeli olduğum için bu konuda yardımcı olamıyorum." verilebilir.

Model, yedek yanıtla karşılık verirse sıcaklığı artırmayı deneyin.

Yapılmaması gerekenler
Doğru bilgiler üretmek için modellere güvenmekten kaçının.
Matematik ve mantık problemlerinde dikkatli kullanın.
Perde arkasındaki üretken modeller
Bu bölümde, Üretken modellerin yanıtlarında rastgelelik var mı yoksa yanıtlar deterministik mi? sorusunun yanıtlanması amaçlanmaktadır.

Kısa yanıt: Her ikisi de evet. Üretken bir modele istem girdiğinizde metin yanıtı iki aşamada oluşturulur. İlk aşamada, üretken model giriş istemini işler ve sonraki olası jetonlar (kelimeler) için bir olasılık dağılımı oluşturur. Örneğin, "Köpek ... üzerinden atladı" giriş metniyle istemde bulunursanız üretken model, olası sonraki kelimeler dizisini oluşturur:


[("fence", 0.77), ("ledge", 0.12), ("blanket", 0.03), ...]
Bu süreç deterministiktir. Üretken model, aynı istem metni her girildiğinde aynı dağıtımı üretir.

İkinci aşamada, üretken model bu dağıtımları çeşitli kod çözme stratejilerinden birini kullanarak gerçek metin yanıtlarına dönüştürür. Basit bir kod çözme stratejisi, her zaman adımında en olası jetonu seçebilir. Bu işlem her zaman deterministik olacaktır. Ancak bunun yerine, modelin döndürdüğü dağılım üzerinde rastgele örnekleme yaparak yanıt oluşturmayı da tercih edebilirsiniz. Bu süreç stokastik (rastgele) olacaktır. Sıcaklığı ayarlayarak bu kod çözme işleminde izin verilen rastgelelik derecesini kontrol edin. Sıcaklık 0 olduğunda yalnızca en olası jetonlar seçilir ve rastgelelik yoktur. Buna karşılık, yüksek sıcaklık, model tarafından seçilen jetonlara yüksek derecede rastgelelik katarak daha beklenmedik ve şaşırtıcı model yanıtlarına yol açar.