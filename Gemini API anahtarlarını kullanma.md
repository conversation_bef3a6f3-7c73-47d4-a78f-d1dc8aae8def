Gemini API anahtarlarını kullanma

Gemini API'yi kullanmak için API anahtarı gerekir. Google Yapay Zeka Studio'da birkaç tıklamayla ücretsiz olarak anahtar oluşturabilirsiniz.

API anahtarınız olduğunda Gemini API'ye bağlanmak için aşağıdaki seçenekleri kullanabilirsiniz:

API anahtarınızı ortam değişkeni olarak ayarlama
API anahtarınızı açıkça sağlama
İlk test için bir API anahtarını sabit kodlayabilirsiniz ancak bu işlem güvenli olmadığından yalnızca geçici olarak yapılmalıdır. API anahtarını sabit kodlamayla ilgili örnekleri API anahtarını açıkça sağlama bölümünde bulabilirsiniz.

API anahtarını ortam değişkeni olarak ayarlama
GEMINI_API_KEY veya GOOGLE_API_KEY ortam değişkenini ayarlarsanız Gemini API kitaplıklarından birini kullanırken API anahtarı istemci tarafından otomatik olarak alınır. Bu değişkenlerden yalnızca birini ayarlamanız önerilir. Ancak her ikisi de ayarlanırsa GOOGLE_API_KEY öncelikli olur.

REST API'yi veya tarayıcıda JavaScript'i kullanıyorsanız API anahtarını açıkça sağlamanız gerekir.

API anahtarınızı farklı işletim sistemlerinde ortam değişkeni olarak yerel olarak nasıl ayarlayabileceğinizi aşağıda bulabilirsiniz. GEMINI_API_KEY

Linux/macOS - Bash
macOS - Zsh
Windows
Bash, yaygın bir Linux ve macOS terminal yapılandırmasıdır. Aşağıdaki komutu çalıştırarak yapılandırma dosyanızın olup olmadığını kontrol edebilirsiniz:


~/.bashrc
Yanıt "No such file or directory" (Böyle bir dosya veya dizin yok) ise bu dosyayı oluşturmanız ve aşağıdaki komutları çalıştırarak açmanız ya da zsh kullanmanız gerekir:


touch ~/.bashrc
open ~/.bashrc
Ardından, aşağıdaki dışa aktarma komutunu ekleyerek API anahtarınızı ayarlamanız gerekir:


export GEMINI_API_KEY=<YOUR_API_KEY_HERE>
Dosyayı kaydettikten sonra aşağıdaki komutu çalıştırarak değişiklikleri uygulayın:


source ~/.bashrc
API anahtarını açıkça sağlama
Bazı durumlarda, API anahtarını açıkça sağlamak isteyebilirsiniz. Örneğin:

Basit bir API çağrısı yapıyorsunuz ve API anahtarını sabit kodlamayı tercih ediyorsunuz.
Gemini API kitaplıklarının ortam değişkenlerini otomatik olarak bulmasına güvenmek zorunda kalmadan açıkça kontrol etmek istiyorsanız
Ortam değişkenlerinin desteklenmediği bir ortam (ör.web) kullanıyorsunuz veya REST çağrıları yapıyorsunuz.
Aşağıda, API anahtarını açıkça nasıl sağlayabileceğinize dair örnekler verilmiştir:

Python
JavaScript
Go
Java
REST

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({ apiKey: "YOUR_API_KEY" });

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "Explain how AI works in a few words",
  });
  console.log(response.text);
}

main();
API anahtarınızın güvenliğini sağlama
Gemini API anahtarınızı şifre gibi kullanın. Tehlikeye girerse başkaları projenizin kotasını kullanabilir, ücretlere neden olabilir (faturalandırma etkinse) ve dosyalar gibi özel verilerinize erişebilir.

Önemli güvenlik kuralları
API anahtarlarını asla kaynak kontrolüne işlemeyin. API anahtarınızı Git gibi sürüm kontrol sistemlerine eklemeyin.

API anahtarlarını hiçbir zaman istemci tarafında kullanmayın. API anahtarınızı üretimdeki web veya mobil uygulamalarda doğrudan kullanmayın. İstemci tarafı kodundaki anahtarlar (JavaScript/TypeScript kitaplıklarımız ve REST çağrıları dahil) ayıklanabilir.

En iyi uygulamalar
API anahtarlarıyla sunucu tarafı çağrıları kullanma API anahtarınızı kullanmanın en güvenli yolu, anahtarın gizli tutulabileceği bir sunucu tarafı uygulamasından Gemini API'yi çağırmaktır.

İstemci tarafı erişim için kısa ömürlü jetonlar kullanma (yalnızca Live API): Live API'ye doğrudan istemci tarafı erişim için kısa ömürlü jetonlar kullanabilirsiniz. Daha düşük güvenlik riskleri içerir ve üretimde kullanıma uygundur. Daha fazla bilgi için geçici jetonlar kılavuzunu inceleyin.

Anahtarınıza kısıtlamalar eklemeyi düşünün: API anahtarı kısıtlamaları ekleyerek anahtarın izinlerini sınırlayabilirsiniz. Bu, anahtarın sızdırılması durumunda olası zararı en aza indirir.

Genel en iyi uygulamalar için bu destek makalesini de inceleyebilirsiniz.