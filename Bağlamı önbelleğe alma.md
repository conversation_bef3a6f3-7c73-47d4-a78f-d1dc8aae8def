Bağlamı önbelleğe alma

Python JavaScript Go REST

Tipik bir yapay zeka iş akışında, aynı giriş jetonlarını bir modele tekrar tekrar iletebilirsiniz. Gemini API iki farklı önbelleğe alma mekanizması sunar:

<PERSON><PERSON><PERSON><PERSON><PERSON> önbelleğe alma (Gemini 2.5 modellerinde otomatik olarak etkinleştirilir, maliyet tasarrufu garantisi yoktur)
Açık önbelleğe alma (Çoğu modelde manuel olarak etkinleştirilebilir, maliyet tasarrufu garantisi)
Açık önbelleğe alma, maliyet tasarrufu sağlamak istediğiniz ancak biraz daha fazla geliştirici çalışması yapmanız gereken durumlarda faydalıdır.

Örtülü önbelleğe alma
Örtülü önbelleğe alma, tüm Gemini 2.5 modellerinde varsayılan olarak etkindir. İsteğiniz önbelleklere isabet ederse maliyet tasarruflarını otomatik olarak aktarırız. Bu özelliği etkinleştirmek için herhangi bir işlem yapmanız gerekmez. 8 Mayıs 2025 itibarıyla geçerlidir. Bağlam önbelleğe alma için minimum giriş jetonu sayısı 2.5 Flash'te 1.024, 2.5 Pro'da ise 4.096'dır.

Örtülü önbellek isabeti olasılığını artırmak için:

Büyük ve yaygın içerikleri isteminizin başına koymayı deneyin.
Kısa süre içinde benzer öneklere sahip istekler göndermeye çalışıyorsunuz.
Yanıt nesnesinin usage_metadata alanında, önbellek isabeti olan jetonların sayısını görebilirsiniz.

Açık önbelleğe alma
Gemini API'nin açık önbelleğe alma özelliğini kullanarak bazı içerikleri modele bir kez iletebilir, giriş jetonlarını önbelleğe alabilir ve ardından sonraki istekler için önbelleğe alınan jetonlara başvurabilirsiniz. Belirli hacimlerde, önbelleğe alınmış jetonları kullanmak, aynı jeton gövdesini tekrar tekrar iletmeye kıyasla daha düşük maliyetlidir.

Bir dizi jetonu önbelleğe aldığınızda, jetonlar otomatik olarak silinmeden önce önbelleğin ne kadar süre boyunca var olmasını istediğinizi seçebilirsiniz. Bu önbelleğe alma süresine geçerlilik süresi (TTL) adı verilir. Ayarlanmazsa TTL varsayılan olarak 1 saat olur. Önbelleğe alma maliyeti, giriş jetonunun boyutuna ve jetonların ne kadar süreyle kalıcı olmasını istediğinize bağlıdır.

Bu bölümde, bir Gemini SDK'sını yüklediğiniz (veya curl'ü yüklediğiniz) ve hızlı başlangıç bölümünde gösterildiği gibi bir API anahtarı yapılandırdığınız varsayılır.

Önbelleği kullanarak içerik oluşturma
Aşağıdaki örnekte, önbelleğe alınmış bir sistem talimatı ve bir metin dosyası kullanarak nasıl içerik oluşturulacağı gösterilmektedir.


import {
  GoogleGenAI,
  createUserContent,
  createPartFromUri,
} from "@google/genai";

const ai = new GoogleGenAI({ apiKey: "GEMINI_API_KEY" });

async function main() {
  const doc = await ai.files.upload({
    file: "path/to/file.txt",
    config: { mimeType: "text/plain" },
  });
  console.log("Uploaded file name:", doc.name);

  const modelName = "gemini-2.0-flash-001";
  const cache = await ai.caches.create({
    model: modelName,
    config: {
      contents: createUserContent(createPartFromUri(doc.uri, doc.mimeType)),
      systemInstruction: "You are an expert analyzing transcripts.",
    },
  });
  console.log("Cache created:", cache);

  const response = await ai.models.generateContent({
    model: modelName,
    contents: "Please summarize this transcript",
    config: { cachedContent: cache.name },
  });
  console.log("Response text:", response.text);
}

await main();
Önbellekleri listeleme
Önbelleğe alınmış içerikleri almak veya görüntülemek mümkün değildir ancak önbellek meta verilerini (name, model, displayName, usageMetadata, createTime, updateTime ve expireTime) alabilirsiniz.

Yüklenen tüm önbelleklerin meta verilerini listelemek için GoogleGenAI.caches.list() kullanın:


console.log("My caches:");
const pager = await ai.caches.list({ config: { pageSize: 10 } });
let page = pager.page;
while (true) {
  for (const c of page) {
    console.log("    ", c.name);
  }
  if (!pager.hasNextPage()) break;
  page = await pager.nextPage();
}
Önbelleği güncelleme
Bir önbellek için yeni bir ttl veya expireTime ayarlayabilirsiniz. Önbellekle ilgili başka bir şeyin değiştirilmesi desteklenmez.

Aşağıdaki örnekte, GoogleGenAI.caches.update() kullanılarak bir önbelleğin ttl değerinin nasıl güncelleneceği gösterilmektedir.


const ttl = `${2 * 3600}s`; // 2 hours in seconds
const updatedCache = await ai.caches.update({
  name: cache.name,
  config: { ttl },
});
console.log("After update (TTL):", updatedCache);
Önbelleği silme
Önbelleğe alma hizmeti, içeriği önbellekten manuel olarak kaldırmak için silme işlemi sağlar. Aşağıdaki örnekte, GoogleGenAI.caches.delete() kullanılarak önbelleğin nasıl silineceği gösterilmektedir.


await ai.caches.delete({ name: cache.name });
OpenAI kitaplığını kullanarak açık önbelleğe alma
OpenAI kitaplığı kullanıyorsanız extra_body üzerinde cached_content özelliğini kullanarak açık önbelleğe almayı etkinleştirebilirsiniz.

Açık önbelleğe alma ne zaman kullanılır?
Bağlamı önbelleğe alma, özellikle kısa istekler tarafından tekrar tekrar başvurulan önemli bir ilk bağlamın olduğu senaryolar için uygundur. Aşağıdaki gibi kullanım alanlarında bağlamı önbelleğe alma özelliğini kullanabilirsiniz:

Kapsamlı sistem talimatları içeren chatbot'lar
Uzun video dosyalarının tekrar tekrar analiz edilmesi
Büyük doküman kümelerine karşı yinelenen sorgular
Sık kod deposu analizi veya hata düzeltme
Açık önbelleğe alma maliyetleri nasıl azaltır?
Bağlam önbelleğe alma, genel işletme maliyetlerini azaltmak için tasarlanmış ücretli bir özelliktir. Faturalandırma aşağıdaki faktörlere göre yapılır:

Önbelleğe alınan jeton sayısı: Önbelleğe alınan giriş jetonlarının sayısı. Bu jetonlar, sonraki istemlere dahil edildiğinde daha düşük bir ücretle faturalandırılır.
Depolama süresi: Önbelleğe alınan jetonların depolandığı süre (TTL), önbelleğe alınan jeton sayısının TTL süresine göre faturalandırılır. TTL için minimum veya maksimum sınır yoktur.
Diğer faktörler: Giriş ve çıkış jetonları gibi önbelleğe alınmamış jetonlar için diğer ücretler geçerlidir.
En güncel fiyatlandırma ayrıntıları için Gemini API fiyatlandırma sayfasını inceleyin. Jetonları nasıl sayacağınızı öğrenmek için Jeton kılavuzuna bakın.

Göz önünde bulundurulacak diğer noktalar
Bağlamı önbelleğe alma özelliğini kullanırken aşağıdaki hususları göz önünde bulundurun:

Bağlam önbelleğe alma için minimum giriş jetonu sayısı 2.5 Flash için 1.024, 2.5 Pro için 2.048'dir. Maksimum, söz konusu model için maksimum değerle aynıdır. (Jeton sayımı hakkında daha fazla bilgi için Jeton kılavuzu başlıklı makaleyi inceleyin.)
Model, önbelleğe alınmış jetonlar ile normal giriş jetonları arasında herhangi bir ayrım yapmaz. Önbelleğe alınmış içerik, istemin ön ekidir.
Bağlam önbelleğe alma konusunda özel bir oran veya kullanım sınırı yoktur. GenerateContent için standart oran sınırları geçerlidir ve jeton sınırlarına önbelleğe alınmış jetonlar da dahildir.
Önbelleğe alınan jeton sayısı, önbellek hizmetinin oluşturma, alma ve listeleme işlemlerinden usage_metadata içinde, önbellek kullanılırken de GenerateContent içinde döndürülür.