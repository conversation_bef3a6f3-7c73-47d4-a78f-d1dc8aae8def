Metin oluşturma

Gemini API, Gemini modellerinden yararlanarak metin, resim, video ve ses gibi çeşitli girişlerden metin çıkışı oluşturabilir.

Tek bir metin girişi alan temel bir örneği aşağıda bulabilirsiniz:

Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "How does AI work?",
  });
  console.log(response.text);
}

await main();

Gemini 2.5 ile düşünme
2.5 Flash ve Pro modellerinde kaliteyi artırmak için varsayılan olarak "düşünme" özelliği etkinleştirilir. Bu özellik, çalışması daha uzun sürebilir ve jeton kullanımını artırabilir.

2.5 Flash'i kullanırken düşünme bütçesini sıfıra ayarlayarak düşünme özelliğini devre dışı bırakabilirsiniz.

Daha fazla ayrıntı için düşünme kılavuzuna bakın.

Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "How does AI work?",
    config: {
      thinkingConfig: {
        thinkingBudget: 0, // Disables thinking
      },
    }
  });
  console.log(response.text);
}

await main();
Sistem talimatları ve diğer yapılandırmalar
Sistem talimatlarıyla Gemini modellerinin davranışını yönlendirebilirsiniz. Bunu yapmak için GenerateContentConfig nesnesi iletin.

Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "Hello there",
    config: {
      systemInstruction: "You are a cat. Your name is Neko.",
    },
  });
  console.log(response.text);
}

await main();
GenerateContentConfig nesnesi, sıcaklık gibi varsayılan oluşturma parametrelerini de geçersiz kılmanıza olanak tanır.

Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: "Explain how AI works",
    config: {
      temperature: 0.1,
    },
  });
  console.log(response.text);
}

await main();
Yapılandırılabilir parametrelerin ve açıklamalarının tam listesi için API referansımızdaki GenerateContentConfig bölümüne bakın.

Çok formatlı girişler
Gemini API, çok formatlı girişleri destekler. Bu sayede metinleri medya dosyalarıyla birleştirebilirsiniz. Aşağıdaki örnekte resim sağlama gösterilmektedir:

Python
JavaScript
Go
REST
Apps Komut Dosyası

import {
  GoogleGenAI,
  createUserContent,
  createPartFromUri,
} from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const image = await ai.files.upload({
    file: "/path/to/organ.png",
  });
  const response = await ai.models.generateContent({
    model: "gemini-2.5-flash",
    contents: [
      createUserContent([
        "Tell me about this instrument",
        createPartFromUri(image.uri, image.mimeType),
      ]),
    ],
  });
  console.log(response.text);
}

await main();
Resim sağlamanın alternatif yöntemleri ve daha gelişmiş resim işleme hakkında bilgi edinmek için Resim Anlama Rehberimizi inceleyin. API, doküman, video ve ses girişlerini ve bu girişlerin anlaşılmasını da destekler.

Yanıtları akış şeklinde gösterme
Varsayılan olarak, model yalnızca tüm oluşturma işlemi tamamlandıktan sonra yanıt verir.

Daha akıcı etkileşimler için, GenerateContentResponse örneklerini oluşturuldukça artımlı olarak almak üzere akışı kullanın.

Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const response = await ai.models.generateContentStream({
    model: "gemini-2.5-flash",
    contents: "Explain how AI works",
  });

  for await (const chunk of response) {
    console.log(chunk.text);
  }
}

await main();
Çok adımlı görüşmeler (Chat)
SDK'larımız, birden fazla istem ve yanıt turunu bir sohbette toplama işlevi sunarak sohbet geçmişini kolayca takip etmenizi sağlar.

Not: Sohbet işlevi yalnızca SDK'ların bir parçası olarak uygulanır. Arka planda generateContent API'si kullanılmaya devam eder. Çok turlu görüşmelerde, her takip turunda görüşme geçmişinin tamamı modele gönderilir.
Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const chat = ai.chats.create({
    model: "gemini-2.5-flash",
    history: [
      {
        role: "user",
        parts: [{ text: "Hello" }],
      },
      {
        role: "model",
        parts: [{ text: "Great to meet you. What would you like to know?" }],
      },
    ],
  });

  const response1 = await chat.sendMessage({
    message: "I have 2 dogs in my house.",
  });
  console.log("Chat response 1:", response1.text);

  const response2 = await chat.sendMessage({
    message: "How many paws are in my house?",
  });
  console.log("Chat response 2:", response2.text);
}

await main();
Yayın, çok adımlı görüşmeler için de kullanılabilir.

Python
JavaScript
Go
REST
Apps Komut Dosyası

import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({});

async function main() {
  const chat = ai.chats.create({
    model: "gemini-2.5-flash",
    history: [
      {
        role: "user",
        parts: [{ text: "Hello" }],
      },
      {
        role: "model",
        parts: [{ text: "Great to meet you. What would you like to know?" }],
      },
    ],
  });

  const stream1 = await chat.sendMessageStream({
    message: "I have 2 dogs in my house.",
  });
  for await (const chunk of stream1) {
    console.log(chunk.text);
    console.log("_".repeat(80));
  }

  const stream2 = await chat.sendMessageStream({
    message: "How many paws are in my house?",
  });
  for await (const chunk of stream2) {
    console.log(chunk.text);
    console.log("_".repeat(80));
  }
}

await main();
Desteklenen modeller
Gemini ailesindeki tüm modeller metin oluşturmayı destekler. Modeller ve özellikleri hakkında daha fazla bilgi edinmek için Modeller sayfasını ziyaret edin.

En iyi uygulamalar
İstem ipuçları
Temel metin oluşturma için örnekler, sistem talimatları veya belirli biçimlendirme gerekmeden genellikle sıfır görevli istem yeterlidir.

Daha özelleştirilmiş çıkışlar için:

Modeli yönlendirmek için Sistem talimatlarını kullanın.
Modeli yönlendirmek için birkaç örnek giriş ve çıkış sağlayın. Bu yönteme genellikle çok görevli istem denir.
Daha fazla ipucu için istem mühendisliği kılavuzumuza göz atın.

Yapılandırılmış çıkış
Bazı durumlarda JSON gibi yapılandırılmış çıkış gerekebilir. Nasıl yapılacağını öğrenmek için yapılandırılmış çıktı kılavuzumuzu inceleyin.